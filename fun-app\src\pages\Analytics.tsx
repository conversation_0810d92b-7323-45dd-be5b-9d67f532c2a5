const Analytics = () => {
  const metrics = [
    {
      title: 'Page Views',
      value: '2.4M',
      change: '+12.5%',
      period: 'vs last month',
      color: 'blue',
    },
    {
      title: 'Unique Visitors',
      value: '180K',
      change: '+8.2%',
      period: 'vs last month',
      color: 'green',
    },
    {
      title: 'Bounce Rate',
      value: '24.3%',
      change: '-2.1%',
      period: 'vs last month',
      color: 'red',
    },
    {
      title: 'Avg. Session Duration',
      value: '4m 32s',
      change: '+15.3%',
      period: 'vs last month',
      color: 'purple',
    },
  ];

  const topPages = [
    { page: '/dashboard', views: '45,231', percentage: '32%' },
    { page: '/products', views: '38,492', percentage: '27%' },
    { page: '/about', views: '22,847', percentage: '16%' },
    { page: '/contact', views: '18,394', percentage: '13%' },
    { page: '/blog', views: '16,829', percentage: '12%' },
  ];

  const trafficSources = [
    { source: 'Organic Search', visitors: '68,432', percentage: '45%', color: 'bg-blue-500' },
    { source: 'Direct', visitors: '42,891', percentage: '28%', color: 'bg-green-500' },
    { source: 'Social Media', visitors: '25,734', percentage: '17%', color: 'bg-purple-500' },
    { source: 'Referral', visitors: '15,234', percentage: '10%', color: 'bg-yellow-500' },
  ];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Analytics</h1>
        <p className="mt-2 text-gray-600">Track your website performance and user engagement.</p>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {metrics.map((metric, index) => (
          <div key={index} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-bold text-gray-900 mt-2">{metric.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-1">{metric.period}</span>
                </div>
              </div>
              <div className={`w-12 h-12 rounded-full bg-${metric.color}-100 flex items-center justify-center`}>
                <svg className={`w-6 h-6 text-${metric.color}-600`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Traffic Chart Placeholder */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Traffic Overview</h3>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p className="mt-2 text-sm text-gray-500">Traffic chart will be displayed here</p>
            </div>
          </div>
        </div>

        {/* Top Pages */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Pages</h3>
          <div className="space-y-4">
            {topPages.map((page, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{page.page}</p>
                  <p className="text-sm text-gray-500">{page.views} views</p>
                </div>
                <div className="text-sm font-medium text-gray-900">{page.percentage}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Traffic Sources */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-6">Traffic Sources</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {trafficSources.map((source, index) => (
            <div key={index} className="text-center">
              <div className={`w-16 h-16 ${source.color} rounded-full mx-auto mb-3 flex items-center justify-center`}>
                <span className="text-white font-bold text-lg">{source.percentage}</span>
              </div>
              <h4 className="text-sm font-medium text-gray-900">{source.source}</h4>
              <p className="text-sm text-gray-500">{source.visitors} visitors</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Analytics;

interface SidebarPage {
  id: string;
  name: string;
  component: React.ComponentType;
  icon: React.ReactElement;
}

interface SidebarProps {
  pages: readonly SidebarPage[];
  currentPage: string;
  setCurrentPage: (page: string) => void;
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
}

const Sidebar = ({ pages, currentPage, setCurrentPage, sidebarOpen, setSidebarOpen }: SidebarProps) => {
  const handleMenuClick = (pageId: string) => {
    setCurrentPage(pageId);
    setSidebarOpen(false); // Close sidebar on mobile after selection
  };

  return (
    <>
      {/* Desktop Sidebar */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col h-0 flex-1 bg-gray-800">
            <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
              <div className="flex items-center flex-shrink-0 px-4">
                <h1 className="text-white text-xl font-semibold">Fun App</h1>
              </div>
              <nav className="mt-5 flex-1 px-2 space-y-1">
                {pages.map((page) => (
                  <button
                    key={page.id}
                    onClick={() => handleMenuClick(page.id)}
                    className={`${
                      currentPage === page.id
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    } group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left transition-colors duration-150 ease-in-out`}
                  >
                    {page.icon}
                    <span className="ml-3">{page.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      } transition-transform duration-300 ease-in-out lg:hidden`}>
        <div className="flex flex-col h-full">
          <div className="flex items-center justify-between flex-shrink-0 px-4 py-4">
            <h1 className="text-white text-xl font-semibold">Fun App</h1>
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-300 hover:text-white"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <nav className="flex-1 px-2 pb-4 space-y-1">
            {pages.map((page) => (
              <button
                key={page.id}
                onClick={() => handleMenuClick(page.id)}
                className={`${
                  currentPage === page.id
                    ? 'bg-gray-900 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                } group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left transition-colors duration-150 ease-in-out`}
              >
                {page.icon}
                <span className="ml-3">{page.name}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;

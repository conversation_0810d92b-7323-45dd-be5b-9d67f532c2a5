{"version": 3, "names": ["_usingCtx", "_disposeSuppressedError", "SuppressedError", "error", "suppressed", "err", "Error", "name", "empty", "stack", "using", "isAwait", "value", "Object", "TypeError", "dispose", "Symbol", "asyncDispose", "undefined", "inner", "call", "e", "Promise", "reject", "push", "v", "d", "a", "u", "bind", "state", "resource", "next", "pop", "resolve", "then", "disposalResult"], "sources": ["../../src/helpers/usingCtx.ts"], "sourcesContent": ["/* @minVersion 7.23.9 */\n\ntype Stack =\n  | {\n      v: Disposable | AsyncDisposable;\n      d: null | undefined | DisposeLike;\n      a: boolean;\n    }\n  | {\n      d: null | undefined;\n      a: true;\n    };\n\ntype DisposeLike = () => void | PromiseLike<void>;\n\ninterface UsingCtxReturn {\n  e: object;\n  u: (value: Disposable | null | undefined) => Disposable | null | undefined;\n  a: (\n    value: AsyncDisposable | Disposable | null | undefined,\n  ) => AsyncDisposable | Disposable | null | undefined;\n  d: DisposeLike;\n}\n\nconst enum StateFlag {\n  NONE = 0,\n  NEEDS_AWAIT = 1,\n  HAS_AWAITED = 2,\n}\n\nexport default function _usingCtx(): UsingCtxReturn {\n  var _disposeSuppressedError =\n      typeof SuppressedError === \"function\"\n        ? SuppressedError\n        : (function (error: Error, suppressed: Error) {\n            var err = new Error() as SuppressedError;\n            err.name = \"SuppressedError\";\n            err.error = error;\n            err.suppressed = suppressed;\n            return err;\n          } as SuppressedErrorConstructor),\n    empty = {},\n    stack: Stack[] = [];\n  function using(\n    isAwait: true,\n    value: AsyncDisposable | Disposable | null | undefined,\n  ): AsyncDisposable | Disposable | null | undefined;\n  function using(\n    isAwait: false,\n    value: Disposable | null | undefined,\n  ): Disposable | null | undefined;\n  function using(\n    isAwait: boolean,\n    value: AsyncDisposable | Disposable | null | undefined,\n  ): AsyncDisposable | Disposable | null | undefined {\n    if (value != null) {\n      if (Object(value) !== value) {\n        throw new TypeError(\n          \"using declarations can only be used with objects, functions, null, or undefined.\",\n        );\n      }\n      // core-js-pure uses Symbol.for for polyfilling well-known symbols\n      if (isAwait) {\n        // value can either be an AsyncDisposable or a Disposable\n        // Try AsyncDisposable first\n        var dispose: DisposeLike | null | undefined = (\n          value as AsyncDisposable\n        )[Symbol.asyncDispose || Symbol[\"for\"](\"Symbol.asyncDispose\")];\n      }\n      if (dispose === undefined) {\n        dispose = (value as Disposable)[\n          Symbol.dispose || Symbol[\"for\"](\"Symbol.dispose\")\n        ];\n        if (isAwait) {\n          var inner = dispose;\n        }\n      }\n      if (typeof dispose !== \"function\") {\n        throw new TypeError(\"Object is not disposable.\");\n      }\n      // @ts-expect-error use before assignment\n      if (inner) {\n        dispose = function () {\n          try {\n            inner.call(value);\n          } catch (e) {\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n            return Promise.reject(e);\n          }\n        };\n      }\n      stack.push({ v: value, d: dispose, a: isAwait });\n    } else if (isAwait) {\n      // provide the nullish `value` as `d` for minification gain\n      stack.push({ d: value, a: isAwait });\n    }\n    return value;\n  }\n  return {\n    // error\n    e: empty,\n    // using\n    u: using.bind(null, false),\n    // await using\n    // full generic signature to avoid type widening\n    a: using.bind<\n      null,\n      [true],\n      [AsyncDisposable | Disposable | null | undefined],\n      AsyncDisposable | Disposable | null | undefined\n    >(null, true),\n    // dispose\n    d: function () {\n      var error = this.e,\n        state: StateFlag = StateFlag.NONE,\n        resource;\n\n      function next(): Promise<void> | void {\n        while ((resource = stack.pop())) {\n          try {\n            if (!resource.a && state === StateFlag.NEEDS_AWAIT) {\n              state = StateFlag.NONE;\n              stack.push(resource);\n              return Promise.resolve().then(next);\n            }\n            if (resource.d) {\n              var disposalResult = resource.d.call(resource.v);\n              if (resource.a) {\n                state |= StateFlag.HAS_AWAITED;\n                return Promise.resolve(disposalResult).then(next, err);\n              }\n            } else {\n              state |= StateFlag.NEEDS_AWAIT;\n            }\n          } catch (e) {\n            return err(e as Error);\n          }\n        }\n        if (state === StateFlag.NEEDS_AWAIT) {\n          if (error !== empty) {\n            // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors\n            return Promise.reject(error);\n          } else {\n            return Promise.resolve();\n          }\n        }\n\n        if (error !== empty) throw error as Error;\n      }\n\n      function err(e: Error): Promise<void> | void {\n        error = error !== empty ? new _disposeSuppressedError(e, error) : e;\n\n        return next();\n      }\n\n      return next();\n    },\n  } satisfies UsingCtxReturn;\n}\n"], "mappings": ";;;;;;AA8Be,SAASA,SAASA,CAAA,EAAmB;EAClD,IAAIC,uBAAuB,GACvB,OAAOC,eAAe,KAAK,UAAU,GACjCA,eAAe,GACd,UAAUC,KAAY,EAAEC,UAAiB,EAAE;MAC1C,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAoB;MACxCD,GAAG,CAACE,IAAI,GAAG,iBAAiB;MAC5BF,GAAG,CAACF,KAAK,GAAGA,KAAK;MACjBE,GAAG,CAACD,UAAU,GAAGA,UAAU;MAC3B,OAAOC,GAAG;IACZ,CAAgC;IACtCG,KAAK,GAAG,CAAC,CAAC;IACVC,KAAc,GAAG,EAAE;EASrB,SAASC,KAAKA,CACZC,OAAgB,EAChBC,KAAsD,EACL;IACjD,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIC,MAAM,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;QAC3B,MAAM,IAAIE,SAAS,CACjB,kFACF,CAAC;MACH;MAEA,IAAIH,OAAO,EAAE;QAGX,IAAII,OAAuC,GACzCH,KAAK,CACLI,MAAM,CAACC,YAAY,IAAID,MAAM,CAAC,KAAK,CAAC,CAAC,qBAAqB,CAAC,CAAC;MAChE;MACA,IAAID,OAAO,KAAKG,SAAS,EAAE;QACzBH,OAAO,GAAIH,KAAK,CACdI,MAAM,CAACD,OAAO,IAAIC,MAAM,CAAC,KAAK,CAAC,CAAC,gBAAgB,CAAC,CAClD;QACD,IAAIL,OAAO,EAAE;UACX,IAAIQ,KAAK,GAAGJ,OAAO;QACrB;MACF;MACA,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;QACjC,MAAM,IAAID,SAAS,CAAC,2BAA2B,CAAC;MAClD;MAEA,IAAIK,KAAK,EAAE;QACTJ,OAAO,GAAG,SAAAA,CAAA,EAAY;UACpB,IAAI;YACFI,KAAK,CAACC,IAAI,CAACR,KAAK,CAAC;UACnB,CAAC,CAAC,OAAOS,CAAC,EAAE;YAEV,OAAOC,OAAO,CAACC,MAAM,CAACF,CAAC,CAAC;UAC1B;QACF,CAAC;MACH;MACAZ,KAAK,CAACe,IAAI,CAAC;QAAEC,CAAC,EAAEb,KAAK;QAAEc,CAAC,EAAEX,OAAO;QAAEY,CAAC,EAAEhB;MAAQ,CAAC,CAAC;IAClD,CAAC,MAAM,IAAIA,OAAO,EAAE;MAElBF,KAAK,CAACe,IAAI,CAAC;QAAEE,CAAC,EAAEd,KAAK;QAAEe,CAAC,EAAEhB;MAAQ,CAAC,CAAC;IACtC;IACA,OAAOC,KAAK;EACd;EACA,OAAO;IAELS,CAAC,EAAEb,KAAK;IAERoB,CAAC,EAAElB,KAAK,CAACmB,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAG1BF,CAAC,EAAEjB,KAAK,CAACmB,IAAI,CAKX,IAAI,EAAE,IAAI,CAAC;IAEbH,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIvB,KAAK,GAAG,IAAI,CAACkB,CAAC;QAChBS,KAAgB,IAAiB;QACjCC,QAAQ;MAEV,SAASC,IAAIA,CAAA,EAAyB;QACpC,OAAQD,QAAQ,GAAGtB,KAAK,CAACwB,GAAG,CAAC,CAAC,EAAG;UAC/B,IAAI;YACF,IAAI,CAACF,QAAQ,CAACJ,CAAC,IAAIG,KAAK,MAA0B,EAAE;cAClDA,KAAK,IAAiB;cACtBrB,KAAK,CAACe,IAAI,CAACO,QAAQ,CAAC;cACpB,OAAOT,OAAO,CAACY,OAAO,CAAC,CAAC,CAACC,IAAI,CAACH,IAAI,CAAC;YACrC;YACA,IAAID,QAAQ,CAACL,CAAC,EAAE;cACd,IAAIU,cAAc,GAAGL,QAAQ,CAACL,CAAC,CAACN,IAAI,CAACW,QAAQ,CAACN,CAAC,CAAC;cAChD,IAAIM,QAAQ,CAACJ,CAAC,EAAE;gBACdG,KAAK,KAAyB;gBAC9B,OAAOR,OAAO,CAACY,OAAO,CAACE,cAAc,CAAC,CAACD,IAAI,CAACH,IAAI,EAAE3B,GAAG,CAAC;cACxD;YACF,CAAC,MAAM;cACLyB,KAAK,KAAyB;YAChC;UACF,CAAC,CAAC,OAAOT,CAAC,EAAE;YACV,OAAOhB,GAAG,CAACgB,CAAU,CAAC;UACxB;QACF;QACA,IAAIS,KAAK,MAA0B,EAAE;UACnC,IAAI3B,KAAK,KAAKK,KAAK,EAAE;YAEnB,OAAOc,OAAO,CAACC,MAAM,CAACpB,KAAK,CAAC;UAC9B,CAAC,MAAM;YACL,OAAOmB,OAAO,CAACY,OAAO,CAAC,CAAC;UAC1B;QACF;QAEA,IAAI/B,KAAK,KAAKK,KAAK,EAAE,MAAML,KAAK;MAClC;MAEA,SAASE,GAAGA,CAACgB,CAAQ,EAAwB;QAC3ClB,KAAK,GAAGA,KAAK,KAAKK,KAAK,GAAG,IAAIP,uBAAuB,CAACoB,CAAC,EAAElB,KAAK,CAAC,GAAGkB,CAAC;QAEnE,OAAOW,IAAI,CAAC,CAAC;MACf;MAEA,OAAOA,IAAI,CAAC,CAAC;IACf;EACF,CAAC;AACH", "ignoreList": []}